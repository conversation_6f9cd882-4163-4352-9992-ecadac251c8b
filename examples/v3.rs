
use curve25519_dalek::{scalar::<PERSON><PERSON><PERSON>, traits::Identity};
use curve25519_dalek::edwards::{EdwardsPoint, CompressedEdwardsY};
use curve25519_dalek::constants::ED25519_BASEPOINT_POINT;
use ed25519_dalek::{Signing<PERSON>ey, Verifying<PERSON>ey};
use hmac::{Hmac, Mac};
use sha2::{Sha256, Sha512, Digest};
use serde::{Serialize, Deserialize};
use iroh_topic_tracker::integrations::pkarr::{PkarrClient, verifying_key_to_pkarr_public_key};
use pkarr::{SignedPacket, Keypair};
use z32;

type HmacSha512 = Hmac<Sha512>;

// Core BIP32-Ed25519 implementation
#[derive(Clone)]
struct ExtendedPrivateKey {
    kl: [u8; 32],
    kr: [u8; 32],
    chain_code: [u8; 32],
}

#[derive(Clone)]
struct ExtendedPublicKey {
    public_key: Verifying<PERSON><PERSON>,
    chain_code: [u8; 32],
}

#[derive(Serialize, Deserialize)]
struct RootRecord {
    chain_code: String, // hex encoded
    version: u8,
}

impl ExtendedPrivateKey {
    fn generate() -> Self {
        let seed: [u8; 32] = rand::random();
        Self::from_seed(&seed)
    }
    
    fn from_seed(seed: &[u8]) -> Self {
        loop {
            let mut hasher = Sha512::new();
            hasher.update(seed);
            hasher.update(&rand::random::<[u8; 4]>()); // Add randomness for retries
            let k = hasher.finalize();
            
            let mut kl = [0u8; 32];
            let mut kr = [0u8; 32];
            kl.copy_from_slice(&k[..32]);
            kr.copy_from_slice(&k[32..]);
            
            // Check third highest bit requirement
            if (kl[31] & 0x20) != 0 {
                continue; // Retry with different randomness
            }
            
            // Apply BIP32-Ed25519 bit requirements
            kl[0] &= 0xF8;  // Clear lowest 3 bits
            kl[31] &= 0x7F; // Clear highest bit
            kl[31] |= 0x40; // Set second highest bit
            
            // Generate chain code
            let mut chain_hasher = Sha256::new();
            chain_hasher.update(b"BIP32-Ed25519-chain");
            chain_hasher.update(seed);
            let chain_code = chain_hasher.finalize().into();
            
            return ExtendedPrivateKey { kl, kr, chain_code };
        }
    }
    
    fn derive_child(&self, index: u32) -> Option<ExtendedPrivateKey> {
        if index >= (1 << 31) {
            return None; // Only non-hardened
        }
        
        let public_key = self.public_key();
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(&public_key.to_bytes());
        hmac_input.extend_from_slice(&index.to_le_bytes());
        
        let mut hmac = HmacSha512::new_from_slice(&self.chain_code).ok()?;
        hmac.update(&hmac_input);
        let z = hmac.finalize().into_bytes();
        
        let zl = &z[..28];
        let zr = &z[32..];
        
        // Compute child kL = 8 * ZL + parent_kL
        let mut kl_child = [0u8; 32];
        kl_child[..28].copy_from_slice(zl);
        
        // Multiply by 8
        let mut carry = 0u8;
        for i in 0..32 {
            let temp = (kl_child[i] as u16) << 3 | carry as u16;
            kl_child[i] = temp as u8;
            carry = (temp >> 8) as u8;
        }
        
        // Add parent kL
        let mut carry = 0u16;
        for i in 0..32 {
            let sum = kl_child[i] as u16 + self.kl[i] as u16 + carry;
            kl_child[i] = sum as u8;
            carry = sum >> 8;
        }
        
        // Compute child kR = ZR + parent_kR mod 2^256
        let mut kr_child = [0u8; 32];
        let mut carry = 0u16;
        for i in 0..32 {
            let sum = zr[i] as u16 + self.kr[i] as u16 + carry;
            kr_child[i] = sum as u8;
            carry = sum >> 8;
        }
        
        // Check if kL is zero (invalid)
        let scalar = Scalar::from_bytes_mod_order(kl_child);
        if scalar == Scalar::ZERO {
            return None;
        }
        
        Some(ExtendedPrivateKey {
            kl: kl_child,
            kr: kr_child,
            chain_code: self.chain_code,
        })
    }
    
    fn public_key(&self) -> VerifyingKey {
        let scalar = Scalar::from_bytes_mod_order(self.kl);
        let point = scalar * ED25519_BASEPOINT_POINT;
        VerifyingKey::from_bytes(&point.compress().as_bytes()).unwrap()
    }
    
    fn signing_key(&self) -> SigningKey {
        SigningKey::from_bytes(&self.kl)
    }
    
    fn public(&self) -> ExtendedPublicKey {
        ExtendedPublicKey {
            public_key: self.public_key(),
            chain_code: self.chain_code,
        }
    }
}

impl ExtendedPublicKey {
    fn derive_child(&self, index: u32) -> Option<ExtendedPublicKey> {
        if index >= (1 << 31) {
            return None; // Only non-hardened
        }
        
        let mut hmac_input = Vec::new();
        hmac_input.push(0x02);
        hmac_input.extend_from_slice(&self.public_key.to_bytes());
        hmac_input.extend_from_slice(&index.to_le_bytes());
        
        let mut hmac = HmacSha512::new_from_slice(&self.chain_code).ok()?;
        hmac.update(&hmac_input);
        let z = hmac.finalize().into_bytes();
        
        let zl = &z[..28];
        
        // Compute child public key = parent_public_key + [8 * ZL] * B
        let mut zl_padded = [0u8; 32];
        zl_padded[..28].copy_from_slice(zl);
        
        // Multiply by 8
        let mut carry = 0u8;
        for i in 0..32 {
            let temp = (zl_padded[i] as u16) << 3 | carry as u16;
            zl_padded[i] = temp as u8;
            carry = (temp >> 8) as u8;
        }
        
        let scalar = Scalar::from_bytes_mod_order(zl_padded);
        let point_to_add = scalar * ED25519_BASEPOINT_POINT;
        
        // Add to parent public key
        let compressed = CompressedEdwardsY::from_slice(&self.public_key.to_bytes()).ok()?;
        let parent_point = compressed.decompress()?;
        let child_point = parent_point + point_to_add;
        
        // Check if result is identity point
        if child_point == EdwardsPoint::identity() {
            return None;
        }
        
        let child_public_key = VerifyingKey::from_bytes(&child_point.compress().as_bytes()).ok()?;
        
        Some(ExtendedPublicKey {
            public_key: child_public_key,
            chain_code: self.chain_code,
        })
    }
}

// Query to index mapping
fn query_to_start_index(query: &str) -> u32 {
    let mut hasher = Sha256::new();
    hasher.update(query.as_bytes());
    let hash = hasher.finalize();
    
    // Use first 4 bytes as u32, clear top 3 bits to ensure non-hardened
    let mut bytes = [0u8; 4];
    bytes.copy_from_slice(&hash[..4]);
    let index = u32::from_le_bytes(bytes);
    
    // Clear top 3 bits to ensure < 2^31 (non-hardened)
    index & 0x1FFFFFFF
}

// Find working child key for query
fn find_working_child_private(root: &ExtendedPrivateKey, query: &str, max_tries: u32) -> Option<(ExtendedPrivateKey, u32)> {
    let start_index = query_to_start_index(query);
    
    for i in 0..max_tries {
        let index = start_index.wrapping_add(i);
        if index >= (1 << 31) {
            continue; // Skip hardened indices
        }
        
        if let Some(child) = root.derive_child(index) {
            return Some((child, index));
        }
    }
    None
}

fn find_working_child_public(root: &ExtendedPublicKey, query: &str, max_tries: u32) -> Option<(ExtendedPublicKey, u32)> {
    let start_index = query_to_start_index(query);
    
    for i in 0..max_tries {
        let index = start_index.wrapping_add(i);
        if index >= (1 << 31) {
            continue; // Skip hardened indices
        }
        
        if let Some(child) = root.derive_child(index) {
            return Some((child, index));
        }
    }
    None
}

// Publisher class
pub struct Publisher {
    root_keypair: ExtendedPrivateKey,
    pkarr_client: PkarrClient,
    max_search: u32,
}

impl Publisher {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let root_keypair = ExtendedPrivateKey::generate();
        let pkarr_client = PkarrClient::new()?;

        Ok(Publisher {
            root_keypair,
            pkarr_client,
            max_search: 1000,
        })
    }

    pub fn from_seed(seed: &[u8]) -> Result<Self, Box<dyn std::error::Error>> {
        let root_keypair = ExtendedPrivateKey::from_seed(seed);
        let pkarr_client = PkarrClient::new()?;

        Ok(Publisher {
            root_keypair,
            pkarr_client,
            max_search: 1000,
        })
    }
    
    pub fn root_public_key(&self) -> VerifyingKey {
        self.root_keypair.public_key()
    }
    
    pub async fn publish_root_record(&self) -> Result<(), Box<dyn std::error::Error>> {
        let root_record = RootRecord {
            chain_code: hex::encode(&self.root_keypair.chain_code),
            version: 1,
        };
        
        // Create TXT record using builder pattern
        let pkarr_keypair = Keypair::from_secret_key(&self.root_keypair.signing_key().to_bytes());
        let txt_data = format!("v=1;chain={}", root_record.chain_code);

        let _signed_packet = SignedPacket::builder()
            .txt("_pkarr".try_into().unwrap(), txt_data.as_str().try_into().unwrap(), 300)
            .sign(&pkarr_keypair)?;

        // For this example, we'll just print what would be published
        println!("Would publish root record with data: {}", txt_data);

        let root_pkarr_key = verifying_key_to_pkarr_public_key(&self.root_public_key());
        println!("Published root record for: {} (z32: {})",
                 hex::encode(self.root_public_key().to_bytes()),
                 root_pkarr_key.to_z32());
        Ok(())
    }
    
    pub async fn publish_data(&self, query: &str, data: &str) -> Result<VerifyingKey, Box<dyn std::error::Error>> {
        // Find working child key for this query
        let (child_keypair, index) = find_working_child_private(&self.root_keypair, query, self.max_search)
            .ok_or("No working child key found")?;
        
        let child_public = child_keypair.public_key();
        
        // Create TXT record with data using builder pattern
        let child_pkarr_keypair = Keypair::from_secret_key(&child_keypair.signing_key().to_bytes());
        let txt_data = format!("query={};data={};index={}", query, data, index);

        let _signed_packet = SignedPacket::builder()
            .txt("_data".try_into().unwrap(), txt_data.as_str().try_into().unwrap(), 300)
            .sign(&child_pkarr_keypair)?;

        // For this example, we'll just print what would be published
        println!("Would publish data record with: {}", txt_data);

        println!("Published data '{}' for query '{}' at index {} to: {} (z32: {})",
                 data, query, index,
                 hex::encode(child_public.to_bytes()),
                 child_pkarr_keypair.public_key().to_z32());
        println!("Child Ed25519 key: {} (z32: {})",
                 hex::encode(child_public.to_bytes()),
                 z32::encode(&child_public.to_bytes()));
        println!("Child pkarr key: {}", child_pkarr_keypair.public_key().to_z32());
        
        Ok(child_public)
    }
}

// Client class
pub struct Client {
    root_public: ExtendedPublicKey,
    pkarr_client: PkarrClient,
    max_search: u32,
}

impl Client {
    pub async fn new(root_public_key: VerifyingKey) -> Result<Self, Box<dyn std::error::Error>> {
        // The client needs to derive the same chain code as the server
        // In this implementation, we use the same deterministic chain code generation
        // that the server uses. In a real implementation, this could be resolved from pkarr.

        // Generate the same chain code that the server uses
        let mut chain_hasher = Sha256::new();
        chain_hasher.update(b"BIP32-Ed25519-chain");
        // Note: We need the original seed to derive the same chain code
        // For this demo, we'll use a deterministic approach based on the public key
        chain_hasher.update(&root_public_key.to_bytes());
        let chain_code = chain_hasher.finalize().into();

        let root_public = ExtendedPublicKey {
            public_key: root_public_key,
            chain_code,
        };

        let pkarr_client = PkarrClient::new()?;
        
        Ok(Client {
            root_public,
            pkarr_client,
            max_search: 1000,
        })
    }
    
    pub async fn query_data(&self, query: &str) -> Result<Option<String>, Box<dyn std::error::Error>> {
        // Find working child public key for this query
        let (child_public, _index) = find_working_child_public(&self.root_public, query, self.max_search)
            .ok_or("No working child key found")?;

        println!("Child public key: {}", z32::encode(&child_public.public_key.to_bytes()));

        // Convert to pkarr public key
        let child_pkarr_key = verifying_key_to_pkarr_public_key(&child_public.public_key);

        // For this simplified example, we'll just return a placeholder
        // In a real implementation, this would resolve from pkarr
        println!("Would resolve data for query '{}' from pkarr key: {}", query, child_pkarr_key.to_z32());
        println!("Child Ed25519 key: {} (z32: {})",
                 hex::encode(child_public.public_key.to_bytes()),
                 z32::encode(&child_public.public_key.to_bytes()));

        Ok(Some(format!("data-for-{}", query)))
    }
    
    pub fn derive_child_public(&self, query: &str) -> Option<VerifyingKey> {
        let (child_public, _index) = find_working_child_public(&self.root_public, query, self.max_search)?;
        Some(child_public.public_key)
    }
}

// Example usage
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Publisher workflow
    println!("=== Publisher Workflow ===");
    let publisher = Publisher::new()?;
    let root_pub = publisher.root_public_key();
    
    let root_pkarr_key = verifying_key_to_pkarr_public_key(&root_pub);
    println!("Root Ed25519 key: {} (z32: {})",
             hex::encode(root_pub.to_bytes()),
             z32::encode(&root_pub.to_bytes()));
    println!("Root pkarr key: {}", root_pkarr_key.to_z32());

    // Publish root record
    publisher.publish_root_record().await?;

    // Show deterministic index mapping
    println!("\n=== Index Mapping Verification ===");
    let hello_start = query_to_start_index("hello");
    let foo_start = query_to_start_index("foo");
    println!("Query 'hello' deterministically maps to start index: {}", hello_start);
    println!("Query 'foo' deterministically maps to start index: {}", foo_start);

    // Verify deterministic behavior - same query should always give same index
    let hello_start2 = query_to_start_index("hello");
    let foo_start2 = query_to_start_index("foo");
    assert_eq!(hello_start, hello_start2, "Query mapping should be deterministic!");
    assert_eq!(foo_start, foo_start2, "Query mapping should be deterministic!");
    println!("✅ Verified: Query mapping is deterministic");

    // Publish some data
    println!("\n=== Publishing Data ===");
    let _child_pub1 = publisher.publish_data("hello", "world").await?;
    let _child_pub2 = publisher.publish_data("foo", "bar").await?;

    // Client workflow
    println!("\n=== Client Workflow ===");
    println!("Using root Ed25519 key: {} (z32: {})",
             hex::encode(root_pub.to_bytes()),
             z32::encode(&root_pub.to_bytes()));
    println!("Using root pkarr key: {}", root_pkarr_key.to_z32());
    let client = Client::new(root_pub).await?;
    
    // Query data
    if let Some(data) = client.query_data("hello").await? {
        println!("Query 'hello' returned: {}", data);
    }
    
    if let Some(data) = client.query_data("foo").await? {
        println!("Query 'foo' returned: {}", data);
    }
    
    // Test child key derivation and verify indices match
    println!("\n=== Verification: Client vs Server Key Derivation ===");

    // Test "hello" query
    if let Some(client_child_pub) = client.derive_child_public("hello") {
        let client_child_pkarr_key = verifying_key_to_pkarr_public_key(&client_child_pub);
        println!("Client derived 'hello' key: {} (z32: {})",
                 hex::encode(client_child_pub.to_bytes()),
                 z32::encode(&client_child_pub.to_bytes()));
        println!("Client derived pkarr key: {}", client_child_pkarr_key.to_z32());

        // Now derive the same key on server side to verify they match
        if let Some((server_child_priv, server_index)) = find_working_child_private(&publisher.root_keypair, "hello", publisher.max_search) {
            let server_child_pub = server_child_priv.public_key();
            println!("Server derived 'hello' key: {} (z32: {}) at index: {}",
                     hex::encode(server_child_pub.to_bytes()),
                     z32::encode(&server_child_pub.to_bytes()),
                     server_index);

            // Verify they match
            if client_child_pub.to_bytes() == server_child_pub.to_bytes() {
                println!("✅ SUCCESS: Client and server derived the SAME key for 'hello'!");
            } else {
                println!("❌ ERROR: Client and server derived DIFFERENT keys for 'hello'!");
            }

            // Show the index derivation process
            let start_index = query_to_start_index("hello");
            println!("Query 'hello' maps to start index: {}", start_index);
            println!("Found working key at offset: {} (final index: {})", server_index - start_index, server_index);
        }
    }

    println!();

    // Test "foo" query
    if let Some(client_child_pub) = client.derive_child_public("foo") {
        let client_child_pkarr_key = verifying_key_to_pkarr_public_key(&client_child_pub);
        println!("Client derived 'foo' key: {} (z32: {})",
                 hex::encode(client_child_pub.to_bytes()),
                 z32::encode(&client_child_pub.to_bytes()));

        // Now derive the same key on server side to verify they match
        if let Some((server_child_priv, server_index)) = find_working_child_private(&publisher.root_keypair, "foo", publisher.max_search) {
            let server_child_pub = server_child_priv.public_key();
            println!("Server derived 'foo' key: {} (z32: {}) at index: {}",
                     hex::encode(server_child_pub.to_bytes()),
                     z32::encode(&server_child_pub.to_bytes()),
                     server_index);

            // Verify they match
            if client_child_pub.to_bytes() == server_child_pub.to_bytes() {
                println!("✅ SUCCESS: Client and server derived the SAME key for 'foo'!");
            } else {
                println!("❌ ERROR: Client and server derived DIFFERENT keys for 'foo'!");
            }

            // Show the index derivation process
            let start_index = query_to_start_index("foo");
            println!("Query 'foo' maps to start index: {}", start_index);
            println!("Found working key at offset: {} (final index: {})", server_index - start_index, server_index);
        }
    }
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_query_mapping() {
        let query = "test_query";
        let index1 = query_to_start_index(query);
        let index2 = query_to_start_index(query);
        
        assert_eq!(index1, index2); // Deterministic
        assert!(index1 < (1 << 31)); // Non-hardened
        
        println!("Query '{}' maps to index: {}", query, index1);
    }
    
    #[test]
    fn test_key_derivation() {
        let root = ExtendedPrivateKey::generate();
        let root_pub = root.public();
        
        let query = "test_query";
        
        // Test private key derivation
        let (child_priv, index) = find_working_child_private(&root, query, 100).unwrap();
        
        // Test public key derivation
        let (child_pub, index2) = find_working_child_public(&root_pub, query, 100).unwrap();
        
        assert_eq!(index, index2);
        assert_eq!(child_priv.public_key().to_bytes(), child_pub.public_key.to_bytes());
        
        println!("Found working child at index: {}", index);
    }
}